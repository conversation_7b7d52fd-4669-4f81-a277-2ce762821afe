# Flow API - Usage Guide

This guide shows how to use all Flow API functionalities in a simple and direct way.

## Initialization

### Option 1: Initialize with specific model (recommended)

```python
from flow_api import FlowAPIClient

# Direct initialization with model - simplest approach
client = FlowAPIClient("gpt-4o-mini")
```

### Option 2: Initialize without model, then set it

```python
from flow_api import FlowAPIClient

# Initialize empty client
client = Flow<PERSON>IClient()

# Set model later
client.with_model("gpt-4o-mini")
```

### Option 3: Initialize by capability

```python
# Find and use a model with specific capability
client = FlowAPIClient()
client.with_capability("chat-conversation")
```

## 0. Connection Check (Recommended)

### Check connection before making requests

```python
# Check connection to avoid delays during actual requests
result = client.check_connection()

if result['status'] == 'connected':
    print(f"✅ {result['message']}")
    # Now make requests - they will be fast since token is ready
else:
    print(f"❌ Connection failed: {result['message']}")
```

**Available Response Fields:**

- `status`: 'connected' or 'error'
- `token_source`: 'cache' (using cached token) or 'new' (obtained new token)
- `message`: Human-readable status message
- `error`: Error details (only if status is 'error')

## 1. List Available Options

### List all models

```python
models = client.list_models()
# May vary by Tenant
```

### List available capabilities

```python
capabilities = client.list_capabilities()

# ['chat-conversation', 'text-embedding', 'image-generation', 'image-recognition', 'speech-to-text', 'streaming', 'system-instruction']
```

### Select any model by capability

```python
client.with_capabilities(["chat-conversation", "system-instruction"])
client.with_capabilities(["text-embedding"])

client.with_capability("image-recognition")
```

## 2. Chat Conversation

### Basic chat

```python
response = client.with_model("gpt-4o-mini").get_answer(
    user_prompt="What are the three laws of robotics?"
)
# Note: If no model is specified (and it may not be specified), the first one from the listing will be used
```

**Available Options:**

- `system_prompt`: System prompt (optional)
- `user_prompt`: User prompt (required)
- `temperature`: Creativity control (0.0-2.0, default varies by model)
- `max_tokens`: Maximum tokens in response
- `top_p`: Diversity control (0.0-1.0)
- `frequency_penalty`: Repetition penalty (-2.0 to 2.0)
- `presence_penalty`: Presence penalty (-2.0 to 2.0)
- `stop`: Stop sequences (string or list)
- `seed`: Seed for reproducibility

### Chat with streaming

```python
for chunk in client.with_model("gpt-4o-mini").get_stream_answer(
    user_prompt="Tell me a short story about a robot."
):
    print(chunk, end='', flush=True)

# Note: Streaming is a capability, check if the selected model supports it.
```

**Available Options:**

- `system_prompt`: System prompt (optional)
- `user_prompt`: User prompt (required)
- `temperature`: Creativity control (0.0-2.0)
- `max_tokens`: Maximum tokens in response
- `top_p`: Diversity control (0.0-1.0)
- `frequency_penalty`: Repetition penalty (-2.0 to 2.0)
- `presence_penalty`: Presence penalty (-2.0 to 2.0)
- `stop`: Stop sequences (string or list)
- `seed`: Seed for reproducibility
- `stream`: Always True for streaming (automatic)

### Chat with system prompt

```python
response = client.with_model("gpt-4o-mini").get_answer(
    system_prompt="You are a helpful assistant that always responds in the style of a pirate.",
    user_prompt="What is the capital of France?"
)

# Note: System Prompt is a capability, check if the selected model supports it.
```

**Available Options:**

- `system_prompt`: System prompt (required in this example)
- `user_prompt`: User prompt (required)
- `temperature`: Creativity control (0.0-2.0)
- `max_tokens`: Maximum tokens in response
- `top_p`: Diversity control (0.0-1.0)
- `frequency_penalty`: Repetition penalty (-2.0 to 2.0)
- `presence_penalty`: Presence penalty (-2.0 to 2.0)
- `stop`: Stop sequences (string or list)
- `seed`: Seed for reproducibility

## 3. Image Generation

```python
response = client.generate_image(
    prompt="A serene mountain landscape at sunset with a crystal clear lake",
    model="dall-e-3",
    size="1024x1024"
)
```

**Available Options:**

- `prompt`: Image description (required)
- `model`: Model to use (optional, auto-selection if not specified)
- `size`: Image size ("256x256", "512x512", "1024x1024", "1792x1024", "1024x1792")
- `quality`: Image quality ("standard", "hd")
- `style`: Image style ("vivid", "natural")
- `n`: Number of images to generate (1-10, default: 1)
- `response_format`: Response format ("url", "b64_json")
- `user`: Unique identifier of the final user

## 4. Image Recognition

### With image URL

```python
messages = [
    {
        "role": "user",
        "content": [
            {
                "type": "text",
                "text": "Please analyze this image in detail."
            },
            {
                "type": "image_url",
                "image_url": {
                    "url": "https://example.com/image.jpg"
                }
            }
        ]
    }
]

response = client.with_model("gpt-4o-mini").get_answer(
    user_prompt=messages
)
```

**Available Options:**

- `system_prompt`: System prompt (optional)
- `user_prompt`: List of mixed content messages (required)
- `temperature`: Creativity control (0.0-2.0)
- `max_tokens`: Maximum tokens in response
- `top_p`: Diversity control (0.0-1.0)
- **Message Structure:**
  - `role`: "user", "system", "assistant"
  - `content`: List of content objects
  - `type`: "text" or "image_url"
  - `image_url.url`: Image URL or data URI base64
  - `image_url.detail`: "low", "high", "auto" (resolution of analysis)

### With image in base64

```python
messages = [
    {
        "role": "user",
        "content": [
            {
                "type": "text",
                "text": "What color is this image?"
            },
            {
                "type": "image_url",
                "image_url": {
                    "url": f"data:image/png;base64,{base64_image_data}"
                }
            }
        ]
    }
]

response = client.with_model("gpt-4o-mini").get_answer(
    user_prompt=messages
)
```

**Available Options:**

- Same options as the previous example
- **Supported Image Formats:** JPEG, PNG, GIF, WebP
- **Maximum Size:** Varies by provider (generally 20MB)
- **Base64 Format:** `data:image/{type};base64,{base64_data}`

## 5. Text Embedding

```python
response = client.generate_embedding(
    text="The quick brown fox jumps over the lazy dog",
    model="text-embedding-ada-002",
    encoding_format="float"
)

embedding = response['data'][0]['embedding']
```

**Available Options:**

- `text`: Text to generate embedding (required)
- `model`: Model to use (optional, auto-selection if not specified)
- `encoding_format`: Encoding format ("float", "base64")
- `dimensions`: Number of embedding dimensions (varies by model)
- `user`: Unique identifier of the final user

## 6. Speech to Text

```python
import base64

# Load audio file
with open("audio.mp3", "rb") as audio_file:
    audio_data = audio_file.read()

audio_base64 = base64.b64encode(audio_data).decode('utf-8')

response = client.generate_transcript(
    audio_data=audio_base64,
    model="whisper",
    language="en",
    response_format="json"
)

# Complete response
transcription = response['combinedPhrases'][0]['text']

# Only text (if available)
transcription = response.get('text', '')
```

**Available Options:**

- `audio_data`: Audio data in base64 (required)
- `model`: Model to use (optional, auto-selection if not specified)
- `language`: Language code ("en", "pt", "es", "fr", etc.)
- `response_format`: Response format ("json", "text", "srt", "verbose_json", "vtt")
- `temperature`: Creativity control (0.0-1.0, default: 0.0)
- `timestamp_granularities`: Timestamp granularities (["word"], ["segment"])
- **Supported Audio Formats:** MP3, MP4, MPEG, MPGA, M4A, WAV, WEBM
- **Maximum Size:** Varies by provider (generally 25MB)

## Important Notes

- Replace model names with those available in your configuration
- Use `client.list_models()` to see all available models
- For streaming responses, use the dedicated `StreamingClient` instead of the main client
- For specific functionalities, use dedicated methods:
  - `generate_embedding()` for embeddings
  - `generate_image()` for image generation
  - `generate_transcript()` for speech-to-text
- The specific methods automatically select an appropriate model if none is specified
- The available options listed for each capability represents all possible settings, but not every model implements them all.
- For specific implementation, see Flow Swagger: https://flow.ciandt.com/ai-orchestration-api/swagger

## Specialized Clients

### Streaming Responses

For real-time streaming responses, use the unified API:

```python
# Initialize with model
client = FlowAPIClient("gpt-4o-mini")

# Option 1: Direct streaming method (recommended)
for chunk in client.get_stream_answer("Tell me a story"):
    print(chunk, end="", flush=True)

# Option 2: Using streaming property (fluent interface)
for chunk in client.streaming.stream("Tell me a story"):
    print(chunk, end="", flush=True)

# Option 3: Direct stream method
for chunk in client.stream("Tell me a story"):
    print(chunk, end="", flush=True)

# With system prompt
for chunk in client.get_stream_answer(
    system_prompt="You are a helpful assistant",
    user_prompt="Tell me about AI"
):
    print(chunk, end="", flush=True)
```

**Available Methods:**

- `get_stream_answer(user_prompt, system_prompt=None, **kwargs)`: Stream responses
- `stream(user_prompt, system_prompt=None, **kwargs)`: Direct stream method
- `streaming.stream(user_prompt, system_prompt=None, **kwargs)`: Fluent interface

### Reasoning Models

For models that provide separated thinking and final answers (DeepSeek-R1, o1):

```python
# Initialize with reasoning model
client = FlowAPIClient("DeepSeek-R1")

# Check if model supports reasoning
if client.supports_reasoning():
    # Get complete reasoning response
    response = client.get_reasoning_answer("Solve step by step: 2x + 5 = 13")

    # Access different parts
    print(f"Thinking: {response.thinking}")
    print(f"Answer: {response.content}")

    # Use convenience methods
    thinking = response.thinking_only()
    answer = response.answer_only()

    # Check if thinking is available
    if response.has_thinking():
        print("Model provided reasoning process")
```

**ReasoningResponse Properties:**

- `.thinking`: The reasoning/thinking process
- `.content`: The final answer
- `.full_content`: Original unprocessed content
- `.full_response`: Complete API response

**ReasoningResponse Methods:**

- `.thinking_only()`: Get only thinking process
- `.answer_only()`: Get only final answer
- `.has_thinking()`: Check if thinking is available
- `.to_dict()`: Convert to dictionary
