"""
Google Gemini response processor for flow_api.

This module provides response processing for Google Gemini services.
"""

from typing import Dict, Any, Optional
from flow_api.adapters.inbound.response_processors.base_processor import BaseResponseProcessor
from flow_api.utils.logging import get_logger

logger = get_logger(__name__)


class GeminiResponseProcessor(BaseResponseProcessor):
    """Response processor for Google Gemini services."""

    def process_response(self, response_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process Google Gemini response and convert to OpenAI format.
        
        Args:
            response_data: Raw response data from Gemini API.
            
        Returns:
            Response in OpenAI format.
        """
        choices = []
        for candidate in response_data.get('candidates', []):
            content_obj = candidate.get('content', {})
            parts = content_obj.get('parts', [])

            # Combine all text parts
            content = ""
            for part in parts:
                if 'text' in part:
                    content += part['text']

            choice = {
                "message": {
                    "role": "assistant",
                    "content": content
                },
                "finish_reason": candidate.get('finishReason', 'stop').lower()
            }
            choices.append(choice)

        return {
            "choices": choices,
            "model": response_data.get("modelVersion", self.model_name),
            "usage": self._convert_gemini_usage(response_data.get("usageMetadata", {}))
        }

    def process_streaming_chunk(self, chunk_data: Dict[str, Any]) -> Optional[str]:
        """Process Google Gemini streaming chunk and extract content.
        
        Args:
            chunk_data: Raw chunk data from Gemini streaming response.
            
        Returns:
            Extracted content string, or None if no content.
        """
        # Handle Gemini streaming format
        candidates = chunk_data.get('candidates', [])
        if candidates:
            candidate = candidates[0]
            content_obj = candidate.get('content', {})
            parts = content_obj.get('parts', [])
            
            content = ""
            for part in parts:
                if 'text' in part:
                    content += part['text']
            
            return content if content else None
        
        return None

    def _convert_gemini_usage(self, usage_metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Convert Gemini usage metadata to OpenAI format.
        
        Args:
            usage_metadata: Gemini usage metadata.
            
        Returns:
            Usage data in OpenAI format.
        """
        return {
            "prompt_tokens": usage_metadata.get("promptTokenCount", 0),
            "completion_tokens": usage_metadata.get("candidatesTokenCount", 0),
            "total_tokens": usage_metadata.get("totalTokenCount", 0)
        }
