"""
Reasoning models response processor for flow_api.

This module provides response processing for reasoning models like Deepseek and o1.
"""

from typing import Dict, Any, Optional
import re
from flow_api.adapters.inbound.response_processors.base_processor import BaseResponseProcessor
from flow_api.utils.logging import get_logger

logger = get_logger(__name__)


class ReasoningResponseProcessor(BaseResponseProcessor):
    """Response processor for reasoning models (Deepseek, o1, etc.)."""

    def __init__(self, model_name: str, base_processor: BaseResponseProcessor):
        """Initialize the reasoning response processor.
        
        Args:
            model_name: The name of the model.
            base_processor: The base processor for the underlying provider.
        """
        super().__init__(model_name)
        self.base_processor = base_processor

    def process_response(self, response_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process reasoning model response and convert to standard format.
        
        Args:
            response_data: Raw response data from the API.
            
        Returns:
            Processed response in standard format.
        """
        # Use base processor first
        processed_response = self.base_processor.process_response(response_data)
        
        # Extract reasoning content if this is a reasoning model
        if self.supports_reasoning():
            reasoning_content = self.extract_reasoning_content(response_data)
            # Add reasoning metadata to the response
            processed_response['reasoning'] = reasoning_content
        
        return processed_response

    def process_streaming_chunk(self, chunk_data: Dict[str, Any]) -> Optional[str]:
        """Process reasoning model streaming chunk and extract content.
        
        Args:
            chunk_data: Raw chunk data from streaming response.
            
        Returns:
            Extracted content string, or None if no content.
        """
        return self.base_processor.process_streaming_chunk(chunk_data)

    def supports_reasoning(self) -> bool:
        """Check if this processor supports reasoning models.
        
        Returns:
            True if this is a reasoning model, False otherwise.
        """
        model_lower = self.model_name.lower()
        return any(keyword in model_lower for keyword in ['deepseek', 'o1', 'reasoning'])

    def extract_reasoning_content(self, response_data: Dict[str, Any]) -> Dict[str, Any]:
        """Extract reasoning content (thinking + final answer) from response.
        
        Args:
            response_data: Raw response data from the API.
            
        Returns:
            Dictionary with 'thinking' and 'content' keys.
        """
        if not self.supports_reasoning():
            return {"content": self.base_processor.process_response(response_data)}

        # Get the processed response
        processed_response = self.base_processor.process_response(response_data)
        
        # Extract content from the first choice
        choices = processed_response.get('choices', [])
        if not choices:
            return {"content": ""}

        message = choices[0].get('message', {})
        full_content = message.get('content', '')

        # Try to separate thinking from final answer
        thinking, final_answer = self._separate_thinking_and_answer(full_content)

        return {
            "thinking": thinking,
            "content": final_answer,
            "full_content": full_content
        }

    def _separate_thinking_and_answer(self, content: str) -> tuple[str, str]:
        """Separate thinking process from final answer.
        
        Args:
            content: Full content from the model response.
            
        Returns:
            Tuple of (thinking, final_answer).
        """
        if not content:
            return "", ""

        # Common patterns for reasoning models
        patterns = [
            # Deepseek patterns
            r'<think>(.*?)</think>\s*(.*)',
            r'<thinking>(.*?)</thinking>\s*(.*)',
            r'思考过程[：:]\s*(.*?)\n\n(.*)',
            r'Let me think[\.:]?\s*(.*?)\n\n(.*)',
            
            # o1 patterns (if they use specific markers)
            r'Reasoning[：:]?\s*(.*?)\n\nAnswer[：:]?\s*(.*)',
            r'Analysis[：:]?\s*(.*?)\n\nConclusion[：:]?\s*(.*)',
            
            # Generic patterns
            r'^\s*([^。！？.!?]*(?:思考|分析|推理|reasoning|thinking|analysis)[^。！？.!?]*[。！？.!?].*?)\n\n(.*)',
        ]

        for pattern in patterns:
            match = re.search(pattern, content, re.DOTALL | re.IGNORECASE)
            if match:
                thinking = match.group(1).strip()
                answer = match.group(2).strip()
                
                # Validate that we found meaningful content
                if len(thinking) > 10 and len(answer) > 10:
                    return thinking, answer

        # If no clear separation found, try to identify thinking by content analysis
        lines = content.split('\n')
        thinking_lines = []
        answer_lines = []
        
        # Look for transition indicators
        transition_found = False
        for i, line in enumerate(lines):
            line_lower = line.lower().strip()
            
            # Check for transition indicators
            if any(indicator in line_lower for indicator in [
                'therefore', 'so', 'thus', 'in conclusion', 'finally',
                '因此', '所以', '综上', '总结', '最终', '答案是'
            ]):
                transition_found = True
                answer_lines.extend(lines[i:])
                break
            
            if not transition_found:
                thinking_lines.append(line)
            else:
                answer_lines.append(line)

        if transition_found and thinking_lines and answer_lines:
            thinking = '\n'.join(thinking_lines).strip()
            answer = '\n'.join(answer_lines).strip()
            
            # Validate lengths
            if len(thinking) > 20 and len(answer) > 20:
                return thinking, answer

        # If still no clear separation, return full content as answer
        return "", content

    def get_thinking_only(self, response_data: Dict[str, Any]) -> str:
        """Get only the thinking/reasoning part of the response.
        
        Args:
            response_data: Raw response data from the API.
            
        Returns:
            Only the thinking/reasoning content.
        """
        reasoning_content = self.extract_reasoning_content(response_data)
        return reasoning_content.get('thinking', '')

    def get_answer_only(self, response_data: Dict[str, Any]) -> str:
        """Get only the final answer part of the response.
        
        Args:
            response_data: Raw response data from the API.
            
        Returns:
            Only the final answer content.
        """
        reasoning_content = self.extract_reasoning_content(response_data)
        return reasoning_content.get('content', '')
