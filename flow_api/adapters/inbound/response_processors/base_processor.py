"""
Base response processor for flow_api.

This module provides the base class for response processors.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from flow_api.utils.logging import get_logger

logger = get_logger(__name__)


class BaseResponseProcessor(ABC):
    """Base class for response processors."""

    def __init__(self, model_name: str):
        """Initialize the response processor.
        
        Args:
            model_name: The name of the model.
        """
        self.model_name = model_name

    @abstractmethod
    def process_response(self, response_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process the API response and convert to standard format.
        
        Args:
            response_data: Raw response data from the API.
            
        Returns:
            Processed response in standard format.
        """
        pass

    @abstractmethod
    def process_streaming_chunk(self, chunk_data: Dict[str, Any]) -> Optional[str]:
        """Process a streaming chunk and extract content.
        
        Args:
            chunk_data: Raw chunk data from streaming response.
            
        Returns:
            Extracted content string, or None if no content.
        """
        pass

    def supports_reasoning(self) -> bool:
        """Check if this processor supports reasoning models.
        
        Returns:
            True if reasoning is supported, False otherwise.
        """
        return False

    def extract_reasoning_content(self, response_data: Dict[str, Any]) -> Dict[str, Any]:
        """Extract reasoning content (thinking + final answer) from response.
        
        Args:
            response_data: Raw response data from the API.
            
        Returns:
            Dictionary with 'thinking' and 'content' keys, or original format if not supported.
        """
        # Default implementation - just return the processed response
        return {"content": self.process_response(response_data)}

    def _convert_usage_to_openai_format(self, usage_data: Dict[str, Any]) -> Dict[str, Any]:
        """Convert usage data to OpenAI format.
        
        Args:
            usage_data: Provider-specific usage data.
            
        Returns:
            Usage data in OpenAI format.
        """
        # Default implementation - assume already in OpenAI format
        return usage_data
