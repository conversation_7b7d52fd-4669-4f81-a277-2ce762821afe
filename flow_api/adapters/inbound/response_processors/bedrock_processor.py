"""
Amazon Bedrock response processor for flow_api.

This module provides response processing for Amazon Bedrock services.
"""

from typing import Dict, Any, Optional
from flow_api.adapters.inbound.response_processors.base_processor import BaseResponseProcessor
from flow_api.utils.logging import get_logger

logger = get_logger(__name__)


class BedrockResponseProcessor(BaseResponseProcessor):
    """Response processor for Amazon Bedrock services."""

    def process_response(self, response_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process Amazon Bedrock response and convert to OpenAI format.
        
        Args:
            response_data: Raw response data from Bedrock API.
            
        Returns:
            Response in OpenAI format.
        """
        content = ""
        stop_reason = "stop"

        if 'output' in response_data:
            # Nova format: output.message.content array
            output = response_data.get('output', {})
            message = output.get('message', {})
            content_data = message.get('content', [])

            if isinstance(content_data, list):
                for item in content_data:
                    if isinstance(item, dict) and 'text' in item:
                        content += item.get('text', '')

            stop_reason = response_data.get('stopReason', 'stop')

        elif 'content' in response_data:
            # Claude format: content is array of objects
            content_data = response_data.get('content', [])
            if isinstance(content_data, list):
                for item in content_data:
                    if isinstance(item, dict) and item.get('type') == 'text':
                        content += item.get('text', '')
            elif isinstance(content_data, dict):
                content = content_data.get('text', '')

            stop_reason = response_data.get('stop_reason', 'stop')

        elif 'generation' in response_data:
            # Llama and other Bedrock models format
            content = response_data.get('generation', '')
            stop_reason = response_data.get('stop_reason', 'stop')

        elif 'outputText' in response_data:
            # Titan format
            content = response_data.get('outputText', '')
            stop_reason = response_data.get('completionReason', 'stop')

        choice = {
            "message": {
                "role": "assistant",
                "content": content
            },
            "finish_reason": stop_reason
        }

        return {
            "choices": [choice],
            "model": response_data.get("model", self.model_name),
            "usage": self._convert_bedrock_usage(response_data)
        }

    def process_streaming_chunk(self, chunk_data: Dict[str, Any]) -> Optional[str]:
        """Process Amazon Bedrock streaming chunk and extract content.
        
        Args:
            chunk_data: Raw chunk data from Bedrock streaming response.
            
        Returns:
            Extracted content string, or None if no content.
        """
        # Handle different Bedrock streaming formats
        if 'delta' in chunk_data and 'text' in chunk_data['delta']:
            # Claude format: delta.text
            return chunk_data['delta']['text']
        elif 'generation' in chunk_data:
            # Llama format: generation
            content = chunk_data['generation']
            return content if content else None
        elif 'outputText' in chunk_data:
            # Titan format: outputText
            return chunk_data['outputText']
        elif 'content' in chunk_data:
            # Nova format: content array
            content_data = chunk_data['content']
            if isinstance(content_data, list):
                text_content = ""
                for item in content_data:
                    if isinstance(item, dict) and 'text' in item:
                        text_content += item['text']
                return text_content if text_content else None
            elif isinstance(content_data, dict) and 'text' in content_data:
                return content_data['text']
        
        return None

    def _convert_bedrock_usage(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """Convert Bedrock usage data to OpenAI format.
        
        Args:
            result: Bedrock API response.
            
        Returns:
            Usage data in OpenAI format.
        """
        prompt_tokens = 0
        completion_tokens = 0

        if 'usage' in result:
            usage = result['usage']

            # Nova format
            if 'inputTokens' in usage:
                prompt_tokens = usage.get("inputTokens", 0)
                completion_tokens = usage.get("outputTokens", 0)
            # Claude format
            elif 'input_tokens' in usage:
                prompt_tokens = usage.get("input_tokens", 0)
                completion_tokens = usage.get("output_tokens", 0)
        else:
            # Other Bedrock models format
            prompt_tokens = result.get("prompt_token_count", 0)
            completion_tokens = result.get("generation_token_count", 0)

        return {
            "prompt_tokens": prompt_tokens,
            "completion_tokens": completion_tokens,
            "total_tokens": prompt_tokens + completion_tokens
        }
