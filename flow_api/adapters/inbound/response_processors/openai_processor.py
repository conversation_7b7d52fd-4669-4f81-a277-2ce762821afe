"""
OpenAI response processor for flow_api.

This module provides response processing for OpenAI services.
"""

from typing import Dict, Any, Optional
from flow_api.adapters.inbound.response_processors.base_processor import BaseResponseProcessor
from flow_api.utils.logging import get_logger

logger = get_logger(__name__)


class OpenAIResponseProcessor(BaseResponseProcessor):
    """Response processor for OpenAI services."""

    def process_response(self, response_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process OpenAI response (already in correct format).
        
        Args:
            response_data: Raw response data from OpenAI API.
            
        Returns:
            Response in OpenAI format (unchanged).
        """
        return response_data

    def process_streaming_chunk(self, chunk_data: Dict[str, Any]) -> Optional[str]:
        """Process OpenAI streaming chunk and extract content.
        
        Args:
            chunk_data: Raw chunk data from OpenAI streaming response.
            
        Returns:
            Extracted content string, or None if no content.
        """
        # Handle OpenAI streaming format
        choices = chunk_data.get('choices', [])
        if choices:
            choice = choices[0]
            if 'delta' in choice and 'content' in choice['delta']:
                content = choice['delta']['content']
                return content if content else None
        
        return None

    def supports_reasoning(self) -> bool:
        """Check if this processor supports reasoning models.
        
        Returns:
            True for o1 models, False otherwise.
        """
        return "o1" in self.model_name.lower()

    def extract_reasoning_content(self, response_data: Dict[str, Any]) -> Dict[str, Any]:
        """Extract reasoning content from o1 models.
        
        Args:
            response_data: Raw response data from OpenAI API.
            
        Returns:
            Dictionary with 'thinking' and 'content' keys for o1 models.
        """
        if not self.supports_reasoning():
            return {"content": self.process_response(response_data)}

        # For o1 models, extract thinking and final content
        choices = response_data.get('choices', [])
        if not choices:
            return {"content": self.process_response(response_data)}

        choice = choices[0]
        message = choice.get('message', {})
        
        # o1 models may have reasoning in a separate field
        thinking = ""
        content = message.get('content', '')
        
        # Check if there's reasoning data in the response
        if 'reasoning' in message:
            thinking = message['reasoning']
        elif 'thoughts' in message:
            thinking = message['thoughts']
        elif hasattr(choice, 'reasoning'):
            thinking = choice['reasoning']

        return {
            "thinking": thinking,
            "content": content,
            "full_response": self.process_response(response_data)
        }
