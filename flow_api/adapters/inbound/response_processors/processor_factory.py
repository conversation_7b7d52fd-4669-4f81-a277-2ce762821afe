"""
Response processor factory for flow_api.

This module provides a factory for creating response processors based on model provider.
"""

from typing import Dict, Any
from flow_api.core.models import AIModel, ModelProvider
from flow_api.adapters.inbound.response_processors.base_processor import BaseResponseProcessor
from flow_api.adapters.inbound.response_processors.openai_processor import OpenAIResponseProcessor
from flow_api.adapters.inbound.response_processors.bedrock_processor import BedrockResponseProcessor
from flow_api.adapters.inbound.response_processors.gemini_processor import GeminiResponseProcessor
from flow_api.adapters.inbound.response_processors.reasoning_processor import ReasoningResponseProcessor
from flow_api.utils.logging import get_logger, log_and_raise
from flow_api.exceptions.client_error import ClientError

logger = get_logger(__name__)


class ResponseProcessorFactory:
    """Factory for creating response processors."""

    @staticmethod
    def create_processor(model: AIModel) -> BaseResponseProcessor:
        """Create a response processor for the given model.
        
        Args:
            model: The AI model to create a processor for.
            
        Returns:
            The appropriate response processor.
            
        Raises:
            ClientError: If no processor is available for the model provider.
        """
        # Create base processor based on provider
        base_processor = ResponseProcessorFactory._create_base_processor(model)
        
        # Check if this is a reasoning model and wrap with reasoning processor
        if ResponseProcessorFactory._is_reasoning_model(model):
            logger.debug(f"Creating reasoning processor for model: {model.name}")
            return ReasoningResponseProcessor(model.name, base_processor)
        
        return base_processor

    @staticmethod
    def _create_base_processor(model: AIModel) -> BaseResponseProcessor:
        """Create base processor based on model provider.
        
        Args:
            model: The AI model.
            
        Returns:
            The base response processor.
            
        Raises:
            ClientError: If no processor is available for the model provider.
        """
        if model.provider == ModelProvider.AZURE_OPENAI:
            return OpenAIResponseProcessor(model.name)
        elif model.provider == ModelProvider.AMAZON_BEDROCK:
            return BedrockResponseProcessor(model.name)
        elif model.provider == ModelProvider.GOOGLE_GEMINI:
            return GeminiResponseProcessor(model.name)
        elif model.provider == ModelProvider.AZURE_FOUNDRY:
            # Azure Foundry typically uses OpenAI-compatible format
            return OpenAIResponseProcessor(model.name)
        else:
            log_and_raise(
                logger,
                f"No response processor available for provider: {model.provider}",
                ClientError,
                "error"
            )

    @staticmethod
    def _is_reasoning_model(model: AIModel) -> bool:
        """Check if the model is a reasoning model.
        
        Args:
            model: The AI model to check.
            
        Returns:
            True if this is a reasoning model, False otherwise.
        """
        model_name_lower = model.name.lower()
        reasoning_keywords = ['deepseek', 'o1', 'reasoning']
        
        return any(keyword in model_name_lower for keyword in reasoning_keywords)

    @staticmethod
    def get_supported_providers() -> list[ModelProvider]:
        """Get list of supported model providers.
        
        Returns:
            List of supported model providers.
        """
        return [
            ModelProvider.AZURE_OPENAI,
            ModelProvider.AMAZON_BEDROCK,
            ModelProvider.GOOGLE_GEMINI,
            ModelProvider.AZURE_FOUNDRY
        ]
