"""
Reasoning response wrapper for flow_api.

This module provides a wrapper for reasoning model responses with convenient access methods.
"""

from typing import Dict, Any, Optional


class ReasoningResponse:
    """Wrapper for reasoning model responses with convenient access methods."""

    def __init__(self, response_data: Dict[str, Any]):
        """Initialize the reasoning response.
        
        Args:
            response_data: Dictionary containing reasoning response data with keys:
                - 'thinking': The reasoning/thinking process
                - 'content': The final answer
                - 'full_response': The complete API response
                - 'full_content': The original unprocessed content
        """
        self._data = response_data

    @property
    def thinking(self) -> str:
        """Get the thinking/reasoning process.
        
        Returns:
            The thinking process content, or empty string if not available.
        """
        return self._data.get('thinking', '')

    @property
    def content(self) -> str:
        """Get the final answer content.
        
        Returns:
            The final answer content.
        """
        return self._data.get('content', '')

    @property
    def full_content(self) -> str:
        """Get the original unprocessed content.
        
        Returns:
            The original content before thinking/answer separation.
        """
        return self._data.get('full_content', '')

    @property
    def full_response(self) -> Dict[str, Any]:
        """Get the complete API response.
        
        Returns:
            The complete API response dictionary.
        """
        return self._data.get('full_response', {})

    def thinking_only(self) -> str:
        """Get only the thinking/reasoning process.
        
        This is a convenience method equivalent to accessing the thinking property.
        
        Returns:
            The thinking process content.
        """
        return self.thinking

    def answer_only(self) -> str:
        """Get only the final answer.
        
        This is a convenience method equivalent to accessing the content property.
        
        Returns:
            The final answer content.
        """
        return self.content

    def has_thinking(self) -> bool:
        """Check if the response contains thinking content.
        
        Returns:
            True if thinking content is available, False otherwise.
        """
        return bool(self.thinking.strip())

    def to_dict(self) -> Dict[str, Any]:
        """Convert the reasoning response to a dictionary.
        
        Returns:
            Dictionary representation of the reasoning response.
        """
        return self._data.copy()

    def __str__(self) -> str:
        """String representation showing both thinking and answer.
        
        Returns:
            Formatted string with thinking and answer sections.
        """
        if self.has_thinking():
            return f"Thinking: {self.thinking}\n\nAnswer: {self.content}"
        else:
            return self.content

    def __repr__(self) -> str:
        """Developer-friendly representation.
        
        Returns:
            String representation for debugging.
        """
        return f"ReasoningResponse(thinking={bool(self.thinking)}, content_length={len(self.content)})"
