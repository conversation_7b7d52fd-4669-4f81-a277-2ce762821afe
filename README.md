# Flow API Python Library

A Python library for interacting with the available Flow AI models through a unified API. This library is intended for internal use only.

Important: The available models and capabilities are subject to change and differ by tenant.

## Installation

```bash
# For local development
pip install -e .
```

```bash
# For production
pip install git+ssh://*****************/ciandt_it/pyflow_api.git
```

## Configuration

Duplicate the `.env.example` file and rename it to `.env`. Then, fill in the required values.

```bash
FLOW_API_CLIENT_ID=your_client_id_here
FLOW_API_CLIENT_SECRET=your_client_secret_here
FLOW_API_APP_TO_ACCESS=your_app_to_access_here
FLOW_API_TENANT=your_flow_tenant_here

# Logging configuration
FLOW_API_LOG_FILE_PATH=/var/log/flow_api/log_file.log  # Set path if you want logs in a file
FLOW_API_CONSOLE_LOG_LEVEL=OFF  # Set to desired level (ALL, INFO, WARNING, ERROR, OFF)
FLOW_API_FILE_LOG_LEVEL=ALL  # Set to desired level (ALL, INFO, WARNING, ERROR, OFF)
```

## Quick Start

```python
from flow_api import FlowAPIClient

# Option 1: Initialize client with a specific model (recommended)
client = FlowAPIClient("gpt-4")

# Option 2: Initialize client without model, then set it
client = FlowAPIClient()
client.with_model("gpt-4")

# Check connection (recommended for better UX)
result = client.check_connection()
if result['status'] == 'connected':
    print("✅ Connected successfully!")
else:
    print(f"❌ Connection failed: {result['message']}")

# Simple text generation (will be fast since token is ready)
response = client.get_answer(
    user_prompt="What is the capital of Brazil?"
)
print(response)
```

## Documentation

For detailed usage examples and all available functionality, see:

- **[USAGE.md](USAGE.md)** - Complete usage guide with examples for all capabilities
- **[examples/](examples/)** - Practical code examples for each functionality
- **[CHANGELOG.md](CHANGELOG.md)** - Model availability history and version changes

### Available Capabilities

- **Chat Conversation** - Text generation with system prompts and streaming
- **Image Recognition** - Analyze images with vision-capable models
- **Image Generation** - Generate images from text prompts
- **Text Embedding** - Convert text to vector embeddings
- **Speech to Text** - Transcribe audio files to text

## Available Models

The following models are currently available (as of 2025-01-16):

| Model                          | Provider        | Capabilities                                            | Input Tokens |
| ------------------------------ | --------------- | ------------------------------------------------------- | ------------ |
| **gpt-4o**                     | Azure OpenAI    | Chat, Image Recognition, Streaming, System Instructions | 128,000      |
| **gpt-4o-mini**                | Azure OpenAI    | Chat, Image Recognition, Streaming, System Instructions | 128,000      |
| **gpt-4.1**                    | Azure OpenAI    | Chat, Image Recognition, Streaming, System Instructions | 1,000,000    |
| **o1**                         | Azure OpenAI    | Chat, Image Recognition, Streaming                      | 200,000      |
| **o1-mini**                    | Azure OpenAI    | Chat, Streaming                                         | 128,000      |
| **o3-mini**                    | Azure OpenAI    | Chat, Streaming, System Instructions                    | 200,000      |
| **text-embedding-ada-002**     | Azure OpenAI    | Text Embedding, Streaming                               | 8,192        |
| **text-embedding-3-small**     | Azure OpenAI    | Text Embedding, Streaming                               | 8,192        |
| **dall-e-3**                   | Azure OpenAI    | Image Generation                                        | 1,000        |
| **DeepSeek-R1**                | Azure Foundry   | Chat, Streaming                                         | 128,000      |
| **gemini-2.5-pro**             | Google Gemini   | Chat, Image Recognition, Streaming, System Instructions | 1,048,576    |
| **gemini-2.0-flash**           | Google Gemini   | Chat, Image Recognition, Streaming, System Instructions | 1,048,576    |
| **textembedding-gecko@003**    | Google Gemini   | Text Embedding, Streaming                               | 8,000        |
| **anthropic.claude-37-sonnet** | Amazon Bedrock  | Chat, Image Recognition, Streaming, System Instructions | 200,000      |
| **meta.llama3-70b-instruct**   | Amazon Bedrock  | Chat, Streaming, System Instructions                    | 8,000        |
| **amazon.nova-lite**           | Amazon Bedrock  | Chat, Image Recognition, Streaming                      | 300,000      |
| **amazon.nova-micro**          | Amazon Bedrock  | Chat, Streaming                                         | 128,000      |
| **amazon.nova-pro**            | Amazon Bedrock  | Chat, Image Recognition, Streaming                      | 300,000      |
| **amazon.titan-embed-text-v2** | Amazon Bedrock  | Text Embedding                                          | 8,191        |
| **whisper**                    | Azure AI Speech | Speech to Text                                          | -            |

> **Capabilities Legend**: Some models have capabilities automatically inferred from their names (e.g., dall-e-3 → Image Generation, whisper → Speech to Text).

> **Note**: Available models may vary by tenant. Use `client.list_models()` to see models available in your configuration.

## Specialized Clients

For specific use cases, use dedicated clients:

### Streaming Responses

```python
# Initialize with model
client = FlowAPIClient("gpt-4o-mini")

# Option 1: Direct streaming method
for chunk in client.get_stream_answer("Tell me a story"):
    print(chunk, end="", flush=True)

# Option 2: Using streaming property (fluent interface)
for chunk in client.streaming.stream("Tell me a story"):
    print(chunk, end="", flush=True)

# Option 3: Direct stream method
for chunk in client.stream("Tell me a story"):
    print(chunk, end="", flush=True)
```

### Reasoning Models

```python
# For models that provide separated thinking and answers (DeepSeek-R1, o1)
client = FlowAPIClient("DeepSeek-R1")

# Get complete reasoning response
response = client.get_reasoning_answer("Solve: 2x + 5 = 13")
print(f"Thinking: {response.thinking}")
print(f"Answer: {response.content}")

# Or get specific parts
thinking = response.thinking_only()
answer = response.answer_only()
```

## Features

- **Dynamic Model Loading**: Models fetched from API (according to tenant) and cached locally
- **Automatic Token Management**: Handles authentication and token refresh, with local caching for faster subsequent requests
- **Capability-Based Selection**: Choose models by what they can do
- **Multi-Modal Support**: Text, images, and speech processing
- **Type Safety**: Full Python type hints
- **Hexagonal Architecture**: Clean, extensible design
