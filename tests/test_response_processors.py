"""
Tests for response processors.
"""

import pytest
from unittest.mock import Mock, patch

from flow_api.adapters.inbound.response_processors.processor_factory import ResponseProcessorFactory
from flow_api.adapters.inbound.response_processors.bedrock_processor import BedrockResponseProcessor
from flow_api.adapters.inbound.response_processors.openai_processor import OpenAIResponseProcessor
from flow_api.adapters.inbound.response_processors.gemini_processor import GeminiResponseProcessor
from flow_api.adapters.inbound.response_processors.reasoning_processor import ReasoningResponseProcessor
from flow_api.core.models import AIModel, ModelProvider
from flow_api.exceptions.client_error import ClientError


class TestResponseProcessorFactory:
    """Test the response processor factory."""

    def test_create_openai_processor(self):
        """Test creating OpenAI processor."""
        model = Mock(spec=AIModel)
        model.name = "gpt-4"
        model.provider = ModelProvider.AZURE_OPENAI
        
        processor = ResponseProcessorFactory.create_processor(model)
        assert isinstance(processor, OpenAIResponseProcessor)
        assert processor.model_name == "gpt-4"

    def test_create_bedrock_processor(self):
        """Test creating Bedrock processor."""
        model = Mock(spec=AIModel)
        model.name = "claude-3-sonnet"
        model.provider = ModelProvider.AMAZON_BEDROCK
        
        processor = ResponseProcessorFactory.create_processor(model)
        assert isinstance(processor, BedrockResponseProcessor)
        assert processor.model_name == "claude-3-sonnet"

    def test_create_gemini_processor(self):
        """Test creating Gemini processor."""
        model = Mock(spec=AIModel)
        model.name = "gemini-pro"
        model.provider = ModelProvider.GOOGLE_GEMINI
        
        processor = ResponseProcessorFactory.create_processor(model)
        assert isinstance(processor, GeminiResponseProcessor)
        assert processor.model_name == "gemini-pro"

    def test_create_reasoning_processor_for_deepseek(self):
        """Test creating reasoning processor for Deepseek model."""
        model = Mock(spec=AIModel)
        model.name = "deepseek-reasoning"
        model.provider = ModelProvider.AZURE_OPENAI
        
        processor = ResponseProcessorFactory.create_processor(model)
        assert isinstance(processor, ReasoningResponseProcessor)
        assert processor.model_name == "deepseek-reasoning"

    def test_create_reasoning_processor_for_o1(self):
        """Test creating reasoning processor for o1 model."""
        model = Mock(spec=AIModel)
        model.name = "o1-preview"
        model.provider = ModelProvider.AZURE_OPENAI
        
        processor = ResponseProcessorFactory.create_processor(model)
        assert isinstance(processor, ReasoningResponseProcessor)
        assert processor.model_name == "o1-preview"

    def test_unsupported_provider_raises_error(self):
        """Test that unsupported provider raises error."""
        model = Mock(spec=AIModel)
        model.name = "unknown-model"
        model.provider = "UNKNOWN_PROVIDER"
        
        with pytest.raises(ClientError):
            ResponseProcessorFactory.create_processor(model)


class TestBedrockResponseProcessor:
    """Test the Bedrock response processor."""

    def test_process_claude_response(self):
        """Test processing Claude response format."""
        processor = BedrockResponseProcessor("claude-3-sonnet")
        
        response_data = {
            "content": [
                {"type": "text", "text": "Hello, how can I help you?"}
            ],
            "stop_reason": "end_turn",
            "usage": {
                "input_tokens": 10,
                "output_tokens": 20
            }
        }
        
        result = processor.process_response(response_data)
        
        assert result["choices"][0]["message"]["content"] == "Hello, how can I help you?"
        assert result["choices"][0]["finish_reason"] == "end_turn"
        assert result["usage"]["prompt_tokens"] == 10
        assert result["usage"]["completion_tokens"] == 20

    def test_process_nova_response(self):
        """Test processing Nova response format."""
        processor = BedrockResponseProcessor("nova-pro")
        
        response_data = {
            "output": {
                "message": {
                    "content": [
                        {"text": "This is a Nova response."}
                    ]
                }
            },
            "stopReason": "stop",
            "usage": {
                "inputTokens": 15,
                "outputTokens": 25
            }
        }
        
        result = processor.process_response(response_data)
        
        assert result["choices"][0]["message"]["content"] == "This is a Nova response."
        assert result["choices"][0]["finish_reason"] == "stop"
        assert result["usage"]["prompt_tokens"] == 15
        assert result["usage"]["completion_tokens"] == 25

    def test_process_llama_response(self):
        """Test processing Llama response format."""
        processor = BedrockResponseProcessor("llama-3")
        
        response_data = {
            "generation": "This is a Llama response.",
            "stop_reason": "stop",
            "prompt_token_count": 12,
            "generation_token_count": 18
        }
        
        result = processor.process_response(response_data)
        
        assert result["choices"][0]["message"]["content"] == "This is a Llama response."
        assert result["choices"][0]["finish_reason"] == "stop"
        assert result["usage"]["prompt_tokens"] == 12
        assert result["usage"]["completion_tokens"] == 18

    def test_process_streaming_chunk_claude(self):
        """Test processing Claude streaming chunk."""
        processor = BedrockResponseProcessor("claude-3-sonnet")
        
        chunk_data = {
            "delta": {
                "text": "Hello"
            }
        }
        
        result = processor.process_streaming_chunk(chunk_data)
        assert result == "Hello"

    def test_process_streaming_chunk_llama(self):
        """Test processing Llama streaming chunk."""
        processor = BedrockResponseProcessor("llama-3")
        
        chunk_data = {
            "generation": "World"
        }
        
        result = processor.process_streaming_chunk(chunk_data)
        assert result == "World"

    def test_process_streaming_chunk_nova(self):
        """Test processing Nova streaming chunk."""
        processor = BedrockResponseProcessor("nova-pro")
        
        chunk_data = {
            "content": [
                {"text": "Nova "},
                {"text": "response"}
            ]
        }
        
        result = processor.process_streaming_chunk(chunk_data)
        assert result == "Nova response"


class TestReasoningResponseProcessor:
    """Test the reasoning response processor."""

    def test_supports_reasoning_for_deepseek(self):
        """Test reasoning support detection for Deepseek."""
        base_processor = Mock()
        processor = ReasoningResponseProcessor("deepseek-reasoning", base_processor)
        
        assert processor.supports_reasoning() is True

    def test_supports_reasoning_for_o1(self):
        """Test reasoning support detection for o1."""
        base_processor = Mock()
        processor = ReasoningResponseProcessor("o1-preview", base_processor)
        
        assert processor.supports_reasoning() is True

    def test_supports_reasoning_for_regular_model(self):
        """Test reasoning support detection for regular model."""
        base_processor = Mock()
        processor = ReasoningResponseProcessor("gpt-4", base_processor)
        
        assert processor.supports_reasoning() is False

    def test_extract_reasoning_with_think_tags(self):
        """Test extracting reasoning content with <think> tags."""
        base_processor = Mock()
        base_processor.process_response.return_value = {
            "choices": [{
                "message": {
                    "content": "<think>Let me analyze this problem step by step.</think>\n\nThe answer is 42."
                }
            }]
        }
        
        processor = ReasoningResponseProcessor("deepseek-reasoning", base_processor)
        
        result = processor.extract_reasoning_content({})
        
        assert result["thinking"] == "Let me analyze this problem step by step."
        assert result["content"] == "The answer is 42."
        assert result["full_content"] == "<think>Let me analyze this problem step by step.</think>\n\nThe answer is 42."

    def test_extract_reasoning_with_thinking_tags(self):
        """Test extracting reasoning content with <thinking> tags."""
        base_processor = Mock()
        base_processor.process_response.return_value = {
            "choices": [{
                "message": {
                    "content": "<thinking>This requires careful consideration.</thinking>\n\nI believe the solution is X."
                }
            }]
        }
        
        processor = ReasoningResponseProcessor("deepseek-reasoning", base_processor)
        
        result = processor.extract_reasoning_content({})
        
        assert result["thinking"] == "This requires careful consideration."
        assert result["content"] == "I believe the solution is X."

    def test_extract_reasoning_without_clear_separation(self):
        """Test extracting reasoning when no clear separation exists."""
        base_processor = Mock()
        base_processor.process_response.return_value = {
            "choices": [{
                "message": {
                    "content": "This is a regular response without thinking tags."
                }
            }]
        }
        
        processor = ReasoningResponseProcessor("deepseek-reasoning", base_processor)
        
        result = processor.extract_reasoning_content({})
        
        assert result["thinking"] == ""
        assert result["content"] == "This is a regular response without thinking tags."
