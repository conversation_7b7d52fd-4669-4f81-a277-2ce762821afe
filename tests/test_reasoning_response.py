"""
Tests for ReasoningResponse class.
"""

import pytest
from flow_api.adapters.inbound.reasoning_response import ReasoningResponse


class TestReasoningResponse:
    """Test the ReasoningResponse wrapper class."""

    def test_init_with_complete_data(self):
        """Test initialization with complete reasoning data."""
        data = {
            "thinking": "Let me analyze this step by step.",
            "content": "The answer is 42.",
            "full_content": "<think>Let me analyze this step by step.</think>\n\nThe answer is 42.",
            "full_response": {"choices": [{"message": {"content": "test"}}]}
        }
        
        response = ReasoningResponse(data)
        
        assert response.thinking == "Let me analyze this step by step."
        assert response.content == "The answer is 42."
        assert response.full_content == "<think>Let me analyze this step by step.</think>\n\nThe answer is 42."
        assert response.full_response == {"choices": [{"message": {"content": "test"}}]}

    def test_init_with_minimal_data(self):
        """Test initialization with minimal data."""
        data = {
            "content": "Simple answer"
        }
        
        response = ReasoningResponse(data)
        
        assert response.thinking == ""
        assert response.content == "Simple answer"
        assert response.full_content == ""
        assert response.full_response == {}

    def test_thinking_only_method(self):
        """Test thinking_only() convenience method."""
        data = {
            "thinking": "Complex reasoning process",
            "content": "Final result"
        }
        
        response = ReasoningResponse(data)
        
        assert response.thinking_only() == "Complex reasoning process"
        assert response.thinking_only() == response.thinking

    def test_answer_only_method(self):
        """Test answer_only() convenience method."""
        data = {
            "thinking": "Complex reasoning process",
            "content": "Final result"
        }
        
        response = ReasoningResponse(data)
        
        assert response.answer_only() == "Final result"
        assert response.answer_only() == response.content

    def test_has_thinking_true(self):
        """Test has_thinking() returns True when thinking is available."""
        data = {
            "thinking": "Some thinking process",
            "content": "Answer"
        }
        
        response = ReasoningResponse(data)
        
        assert response.has_thinking() is True

    def test_has_thinking_false_empty(self):
        """Test has_thinking() returns False when thinking is empty."""
        data = {
            "thinking": "",
            "content": "Answer"
        }
        
        response = ReasoningResponse(data)
        
        assert response.has_thinking() is False

    def test_has_thinking_false_whitespace(self):
        """Test has_thinking() returns False when thinking is only whitespace."""
        data = {
            "thinking": "   \n\t  ",
            "content": "Answer"
        }
        
        response = ReasoningResponse(data)
        
        assert response.has_thinking() is False

    def test_has_thinking_false_missing(self):
        """Test has_thinking() returns False when thinking is missing."""
        data = {
            "content": "Answer"
        }
        
        response = ReasoningResponse(data)
        
        assert response.has_thinking() is False

    def test_to_dict(self):
        """Test to_dict() method returns copy of data."""
        data = {
            "thinking": "Thinking process",
            "content": "Answer",
            "full_content": "Full content"
        }
        
        response = ReasoningResponse(data)
        result_dict = response.to_dict()
        
        assert result_dict == data
        assert result_dict is not data  # Should be a copy

    def test_str_with_thinking(self):
        """Test string representation with thinking content."""
        data = {
            "thinking": "Let me think about this.",
            "content": "The answer is X."
        }
        
        response = ReasoningResponse(data)
        
        expected = "Thinking: Let me think about this.\n\nAnswer: The answer is X."
        assert str(response) == expected

    def test_str_without_thinking(self):
        """Test string representation without thinking content."""
        data = {
            "thinking": "",
            "content": "The answer is X."
        }
        
        response = ReasoningResponse(data)
        
        assert str(response) == "The answer is X."

    def test_repr(self):
        """Test developer-friendly representation."""
        data = {
            "thinking": "Some thinking",
            "content": "Answer here"
        }
        
        response = ReasoningResponse(data)
        
        expected = "ReasoningResponse(thinking=True, content_length=11)"
        assert repr(response) == expected

    def test_repr_no_thinking(self):
        """Test repr without thinking content."""
        data = {
            "thinking": "",
            "content": "Answer"
        }
        
        response = ReasoningResponse(data)
        
        expected = "ReasoningResponse(thinking=False, content_length=6)"
        assert repr(response) == expected

    def test_property_access_patterns(self):
        """Test various property access patterns."""
        data = {
            "thinking": "Analysis process",
            "content": "Final answer",
            "full_content": "Complete content",
            "full_response": {"model": "test"}
        }
        
        response = ReasoningResponse(data)
        
        # Test direct property access
        assert response.thinking == "Analysis process"
        assert response.content == "Final answer"
        
        # Test method access
        assert response.thinking_only() == "Analysis process"
        assert response.answer_only() == "Final answer"
        
        # Test utility methods
        assert response.has_thinking() is True
        assert len(response.to_dict()) == 4

    def test_empty_response(self):
        """Test handling of completely empty response."""
        response = ReasoningResponse({})
        
        assert response.thinking == ""
        assert response.content == ""
        assert response.full_content == ""
        assert response.full_response == {}
        assert response.has_thinking() is False
        assert response.thinking_only() == ""
        assert response.answer_only() == ""
        assert str(response) == ""
