"""
Tests for streaming client.
"""

import pytest
from unittest.mock import Mock, patch, MagicMock
import json
import base64

from flow_api.adapters.inbound.streaming_client import StreamingClient
from flow_api.core.capabilities import Capability
from flow_api.exceptions.client_error import ClientError


class TestStreamingClient:
    """Test the streaming client."""

    @patch('flow_api.adapters.inbound.streaming_client.ModelFactory')
    @patch('flow_api.adapters.inbound.streaming_client.ResponseProcessorFactory')
    def test_with_model_success(self, mock_processor_factory, mock_model_factory):
        """Test setting model successfully."""
        # Mock model with streaming capability
        mock_model = Mock()
        mock_model.has_capability.return_value = True
        
        # Mock manager
        mock_manager = Mock()
        mock_manager.model = mock_model
        mock_manager.get_client.return_value = Mock()
        
        mock_model_factory.create_client_manager.return_value = mock_manager
        mock_processor_factory.create_processor.return_value = Mock()
        
        client = StreamingClient()
        result = client.with_model("test-model")
        
        assert result is client
        assert client._current_manager is mock_manager
        mock_model.has_capability.assert_called_with(Capability.STREAMING)

    @patch('flow_api.adapters.inbound.streaming_client.ModelFactory')
    def test_with_model_no_streaming_capability(self, mock_model_factory):
        """Test setting model without streaming capability raises error."""
        # Mock model without streaming capability
        mock_model = Mock()
        mock_model.has_capability.return_value = False
        
        # Mock manager
        mock_manager = Mock()
        mock_manager.model = mock_model
        
        mock_model_factory.create_client_manager.return_value = mock_manager
        
        client = StreamingClient()
        
        with pytest.raises(ClientError, match="does not support streaming capability"):
            client.with_model("test-model")

    @patch('requests.post')
    @patch('flow_api.adapters.outbound.auth.token_manager.TokenManager')
    @patch('flow_api.config.Config')
    @patch('flow_api.adapters.inbound.streaming_client.ModelRegistry')
    @patch('flow_api.adapters.inbound.streaming_client.ModelFactory')
    @patch('flow_api.adapters.inbound.streaming_client.ResponseProcessorFactory')
    def test_stream_openai_format(self, mock_processor_factory, mock_model_factory,
                                  mock_model_registry, mock_config, mock_token_manager, mock_requests):
        """Test streaming with OpenAI format response."""
        # Setup mocks
        mock_model = Mock()
        mock_model.has_capability.return_value = True
        mock_model.provider = "AZURE_OPENAI"
        mock_model.name = "gpt-4"
        
        mock_manager = Mock()
        mock_manager.model = mock_model
        
        mock_client = Mock()
        mock_client.model = mock_model
        mock_client.build_request_payload.return_value = {"messages": []}
        mock_client.get_endpoint_path.return_value = "/chat/completions"
        
        mock_manager.get_client.return_value = mock_client
        mock_model_factory.create_client_manager.return_value = mock_manager
        
        mock_processor = Mock()
        mock_processor.process_streaming_chunk.side_effect = ["Hello", " world", "!"]
        mock_processor_factory.create_processor.return_value = mock_processor
        
        mock_config_instance = Mock()
        mock_config_instance.api_base_url = "https://api.example.com"
        mock_config_instance.flow_tenant = "test-tenant"
        mock_config.return_value = mock_config_instance
        
        mock_token_manager_instance = Mock()
        mock_token_manager_instance.get_valid_token.return_value = "test-token"
        mock_token_manager.return_value = mock_token_manager_instance
        
        # Mock streaming response
        mock_response = Mock()
        mock_response.iter_lines.return_value = [
            b'data: {"choices":[{"delta":{"content":"Hello"}}]}',
            b'data: {"choices":[{"delta":{"content":" world"}}]}',
            b'data: {"choices":[{"delta":{"content":"!"}}]}',
            b'data: [DONE]'
        ]
        mock_requests.return_value = mock_response
        
        # Test streaming
        client = StreamingClient("gpt-4")
        chunks = list(client.stream("Hello"))
        
        assert chunks == ["Hello", " world", "!"]
        mock_requests.assert_called_once()

    @patch('requests.post')
    @patch('flow_api.adapters.outbound.auth.token_manager.TokenManager')
    @patch('flow_api.config.Config')
    @patch('flow_api.adapters.inbound.streaming_client.ModelRegistry')
    @patch('flow_api.adapters.inbound.streaming_client.ModelFactory')
    @patch('flow_api.adapters.inbound.streaming_client.ResponseProcessorFactory')
    def test_stream_bedrock_format(self, mock_processor_factory, mock_model_factory,
                                   mock_model_registry, mock_config, mock_token_manager, mock_requests):
        """Test streaming with Amazon Bedrock format response."""
        # Setup mocks
        mock_model = Mock()
        mock_model.has_capability.return_value = True
        mock_model.provider = "AMAZON_BEDROCK"
        mock_model.name = "claude-3-sonnet"
        
        mock_manager = Mock()
        mock_manager.model = mock_model
        
        mock_client = Mock()
        mock_client.model = mock_model
        mock_client.build_request_payload.return_value = {"messages": []}
        mock_client.get_endpoint_path.return_value = "/bedrock/invoke"
        
        mock_manager.get_client.return_value = mock_client
        mock_model_factory.create_client_manager.return_value = mock_manager
        
        mock_processor = Mock()
        mock_processor.process_streaming_chunk.side_effect = ["Hello", " from", " Claude"]
        mock_processor_factory.create_processor.return_value = mock_processor
        
        mock_config_instance = Mock()
        mock_config_instance.api_base_url = "https://api.example.com"
        mock_config_instance.flow_tenant = "test-tenant"
        mock_config_instance.get_endpoint_path.return_value = "/bedrock/invoke-with-response-stream"
        mock_config.return_value = mock_config_instance
        
        mock_token_manager_instance = Mock()
        mock_token_manager_instance.get_valid_token.return_value = "test-token"
        mock_token_manager.return_value = mock_token_manager_instance
        
        # Create base64 encoded Bedrock chunks
        chunk1_data = json.dumps({"delta": {"text": "Hello"}})
        chunk1_b64 = base64.b64encode(chunk1_data.encode()).decode()
        
        chunk2_data = json.dumps({"delta": {"text": " from"}})
        chunk2_b64 = base64.b64encode(chunk2_data.encode()).decode()
        
        chunk3_data = json.dumps({"delta": {"text": " Claude"}})
        chunk3_b64 = base64.b64encode(chunk3_data.encode()).decode()
        
        # Mock streaming response
        mock_response = Mock()
        mock_response.iter_lines.return_value = [
            f':message-typeevent{{"bytes":"{chunk1_b64}"}}'.encode(),
            f':message-typeevent{{"bytes":"{chunk2_b64}"}}'.encode(),
            f':message-typeevent{{"bytes":"{chunk3_b64}"}}'.encode(),
            b'data: [DONE]'
        ]
        mock_requests.return_value = mock_response

        # Test streaming
        client = StreamingClient("claude-3-sonnet")
        chunks = list(client.stream("Hello"))

        assert chunks == ["Hello", " from", " Claude"]
        mock_requests.assert_called_once()

    def test_stream_no_model_raises_error(self):
        """Test streaming without model raises error."""
        client = StreamingClient()
        
        with pytest.raises(ClientError, match="No model selected"):
            list(client.stream("Hello"))

    @patch('flow_api.adapters.inbound.streaming_client.ModelFactory')
    @patch('flow_api.adapters.inbound.streaming_client.ResponseProcessorFactory')
    def test_get_current_model(self, mock_processor_factory, mock_model_factory):
        """Test getting current model name."""
        mock_model = Mock()
        mock_model.has_capability.return_value = True
        mock_model.name = "test-model"
        
        mock_manager = Mock()
        mock_manager.model = mock_model
        mock_manager.get_client.return_value = Mock()
        
        mock_model_factory.create_client_manager.return_value = mock_manager
        mock_processor_factory.create_processor.return_value = Mock()
        
        client = StreamingClient()
        assert client.get_current_model() is None
        
        client.with_model("test-model")
        assert client.get_current_model() == "test-model"

    @patch('flow_api.adapters.inbound.streaming_client.ModelFactory')
    @patch('flow_api.adapters.inbound.streaming_client.ResponseProcessorFactory')
    def test_supports_streaming(self, mock_processor_factory, mock_model_factory):
        """Test checking streaming support."""
        mock_model = Mock()
        mock_model.has_capability.return_value = True
        
        mock_manager = Mock()
        mock_manager.model = mock_model
        mock_manager.get_client.return_value = Mock()
        
        mock_model_factory.create_client_manager.return_value = mock_manager
        mock_processor_factory.create_processor.return_value = Mock()
        
        client = StreamingClient()
        assert client.supports_streaming() is False
        
        client.with_model("test-model")
        assert client.supports_streaming() is True

    def test_extract_legacy_streaming_content_openai(self):
        """Test legacy content extraction for OpenAI format."""
        client = StreamingClient()
        
        chunk_data = {
            "choices": [{
                "delta": {
                    "content": "Hello world"
                }
            }]
        }
        
        result = client._extract_legacy_streaming_content(chunk_data)
        assert result == "Hello world"

    def test_extract_legacy_streaming_content_bedrock_bytes(self):
        """Test legacy content extraction for Bedrock bytes format."""
        client = StreamingClient()
        
        # Create base64 encoded data
        bedrock_data = json.dumps({"delta": {"text": "Hello Bedrock"}})
        encoded_data = base64.b64encode(bedrock_data.encode()).decode()
        
        chunk_data = {
            "bytes": encoded_data
        }
        
        result = client._extract_legacy_streaming_content(chunk_data)
        assert result == "Hello Bedrock"

    def test_extract_legacy_streaming_content_no_content(self):
        """Test legacy content extraction when no content available."""
        client = StreamingClient()
        
        chunk_data = {
            "metadata": "some metadata"
        }
        
        result = client._extract_legacy_streaming_content(chunk_data)
        assert result is None
