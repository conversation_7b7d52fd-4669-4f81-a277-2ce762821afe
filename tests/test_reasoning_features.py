"""
Tests for reasoning features in FlowAPIClient.
"""

from unittest.mock import Mock, patch

from flow_api import FlowAPIClient
from flow_api.adapters.inbound.reasoning_response import ReasoningResponse


class TestReasoningFeatures:
    """Test reasoning features in FlowAPIClient."""

    @patch('flow_api.adapters.inbound.client.ModelFactory')
    @patch('flow_api.adapters.inbound.client.ResponseProcessorFactory')
    def test_get_reasoning_answer_with_reasoning_model(self, mock_processor_factory, mock_model_factory):
        """Test getting reasoning answer from reasoning model."""
        # Mock reasoning processor
        mock_processor = Mock()
        mock_processor.extract_reasoning_content.return_value = {
            "thinking": "Let me think about this step by step.",
            "content": "The answer is 42.",
            "full_content": "<think>Let me think about this step by step.</think>\n\nThe answer is 42."
        }
        mock_processor_factory.create_processor.return_value = mock_processor

        # Mock model manager
        mock_manager = Mock()
        mock_manager.model = Mock()
        mock_manager.get_client.return_value = Mock()
        mock_model_factory.create_client_manager.return_value = mock_manager

        # Mock API response
        api_response = {
            "choices": [{
                "message": {
                    "content": "<think>Let me think about this step by step.</think>\n\nThe answer is 42."
                }
            }]
        }

        client = FlowAPIClient("deepseek-reasoning")

        with patch.object(client, 'send', return_value=api_response):
            result = client.get_reasoning_answer("What is the meaning of life?")

        assert isinstance(result, ReasoningResponse)
        assert result.thinking == "Let me think about this step by step."
        assert result.content == "The answer is 42."
        assert result.full_response == api_response
        assert result.full_content == "<think>Let me think about this step by step.</think>\n\nThe answer is 42."

    @patch('flow_api.adapters.inbound.client.ModelFactory')
    @patch('flow_api.adapters.inbound.client.ResponseProcessorFactory')
    def test_get_reasoning_answer_with_regular_model(self, mock_processor_factory, mock_model_factory):
        """Test getting reasoning answer from regular model."""
        # Mock regular processor (no reasoning support)
        mock_processor = Mock()
        # Remove the extract_reasoning_content method to simulate no reasoning support
        del mock_processor.extract_reasoning_content
        mock_processor_factory.create_processor.return_value = mock_processor

        # Mock model manager
        mock_manager = Mock()
        mock_manager.model = Mock()
        mock_manager.get_client.return_value = Mock()
        mock_model_factory.create_client_manager.return_value = mock_manager

        # Mock API response
        api_response = {
            "choices": [{
                "message": {
                    "content": "The answer is 42."
                }
            }]
        }

        client = FlowAPIClient("gpt-4")

        with patch.object(client, 'send', return_value=api_response):
            result = client.get_reasoning_answer("What is the meaning of life?")

        assert isinstance(result, ReasoningResponse)
        assert result.thinking == ""
        assert result.content == "The answer is 42."
        assert result.full_response == api_response
        assert result.full_content == "The answer is 42."

    @patch('flow_api.adapters.inbound.client.ModelFactory')
    @patch('flow_api.adapters.inbound.client.ResponseProcessorFactory')
    def test_thinking_only_method(self, mock_processor_factory, mock_model_factory):
        """Test getting only thinking part using thinking_only() method."""
        # Mock reasoning processor
        mock_processor = Mock()
        mock_processor.extract_reasoning_content.return_value = {
            "thinking": "This requires careful analysis.",
            "content": "Final answer here."
        }
        mock_processor_factory.create_processor.return_value = mock_processor

        # Mock model manager
        mock_manager = Mock()
        mock_manager.model = Mock()
        mock_manager.get_client.return_value = Mock()
        mock_model_factory.create_client_manager.return_value = mock_manager

        # Mock API response
        api_response = {
            "choices": [{
                "message": {
                    "content": "<thinking>This requires careful analysis.</thinking>\n\nFinal answer here."
                }
            }]
        }

        client = FlowAPIClient("deepseek-reasoning")

        with patch.object(client, 'send', return_value=api_response):
            result = client.get_reasoning_answer("Analyze this problem")
            thinking = result.thinking_only()

        assert thinking == "This requires careful analysis."

    @patch('flow_api.adapters.inbound.client.ModelFactory')
    @patch('flow_api.adapters.inbound.client.ResponseProcessorFactory')
    def test_answer_only_method(self, mock_processor_factory, mock_model_factory):
        """Test getting only final answer using answer_only() method."""
        # Mock reasoning processor
        mock_processor = Mock()
        mock_processor.extract_reasoning_content.return_value = {
            "thinking": "This requires careful analysis.",
            "content": "Final answer here."
        }
        mock_processor_factory.create_processor.return_value = mock_processor

        # Mock model manager
        mock_manager = Mock()
        mock_manager.model = Mock()
        mock_manager.get_client.return_value = Mock()
        mock_model_factory.create_client_manager.return_value = mock_manager

        # Mock API response
        api_response = {
            "choices": [{
                "message": {
                    "content": "<thinking>This requires careful analysis.</thinking>\n\nFinal answer here."
                }
            }]
        }

        client = FlowAPIClient("deepseek-reasoning")

        with patch.object(client, 'send', return_value=api_response):
            result = client.get_reasoning_answer("Analyze this problem")
            answer = result.answer_only()

        assert answer == "Final answer here."

    @patch('flow_api.adapters.inbound.client.ModelFactory')
    @patch('flow_api.adapters.inbound.client.ResponseProcessorFactory')
    def test_supports_reasoning_true(self, mock_processor_factory, mock_model_factory):
        """Test reasoning support detection returns True."""
        # Mock reasoning processor
        mock_processor = Mock()
        mock_processor.supports_reasoning.return_value = True
        mock_processor_factory.create_processor.return_value = mock_processor
        
        # Mock model manager
        mock_manager = Mock()
        mock_manager.model = Mock()
        mock_manager.get_client.return_value = Mock()
        mock_model_factory.create_client_manager.return_value = mock_manager
        
        client = FlowAPIClient("deepseek-reasoning")
        
        assert client.supports_reasoning() is True

    @patch('flow_api.adapters.inbound.client.ModelFactory')
    @patch('flow_api.adapters.inbound.client.ResponseProcessorFactory')
    def test_supports_reasoning_false(self, mock_processor_factory, mock_model_factory):
        """Test reasoning support detection returns False."""
        # Mock regular processor
        mock_processor = Mock()
        mock_processor.supports_reasoning.return_value = False
        mock_processor_factory.create_processor.return_value = mock_processor
        
        # Mock model manager
        mock_manager = Mock()
        mock_manager.model = Mock()
        mock_manager.get_client.return_value = Mock()
        mock_model_factory.create_client_manager.return_value = mock_manager
        
        client = FlowAPIClient("gpt-4")
        
        assert client.supports_reasoning() is False

    @patch('flow_api.adapters.inbound.client.ModelFactory')
    @patch('flow_api.adapters.inbound.client.ResponseProcessorFactory')
    def test_supports_reasoning_no_processor(self, mock_processor_factory, mock_model_factory):
        """Test reasoning support when no processor available."""
        mock_processor_factory.create_processor.return_value = None
        
        # Mock model manager
        mock_manager = Mock()
        mock_manager.model = Mock()
        mock_manager.get_client.return_value = Mock()
        mock_model_factory.create_client_manager.return_value = mock_manager
        
        client = FlowAPIClient("gpt-4")
        client._response_processor = None
        
        assert client.supports_reasoning() is False



