"""
Examples demonstrating reasoning models functionality.

This script shows how to use reasoning models like Deepseek and o1 that provide
separated thinking process and final answers.
"""

import os
from flow_api import FlowAPIClient
from flow_api.adapters.inbound.streaming_client import StreamingClient


def example_basic_reasoning():
    """Demonstrate basic reasoning model capabilities."""
    print("=== Basic Reasoning Examples ===\n")
    
    # Initialize client with a reasoning model
    client = FlowAPIClient("deepseek-reasoning")
    
    # Check if current model supports reasoning
    if client.supports_reasoning():
        print("✅ Current model supports reasoning capabilities")
    else:
        print("❌ Current model does not support reasoning")
        print("Switching to a regular model for demonstration...")
        client = FlowAPIClient("gpt-4")
    
    prompt = "Solve this step by step: What is 15% of 240?"
    
    # Example 1: Get complete reasoning response
    print("\n1. Complete reasoning response:")
    try:
        reasoning_response = client.get_reasoning_answer(user_prompt=prompt)
        
        print(f"Thinking process: {reasoning_response.thinking}")
        print(f"Final answer: {reasoning_response.content}")
        print(f"Has thinking: {reasoning_response.has_thinking()}")
        
    except Exception as e:
        print(f"Error: {e}")
    
    # Example 2: Get only the thinking process
    print("\n2. Only thinking process:")
    try:
        reasoning_response = client.get_reasoning_answer(user_prompt=prompt)
        thinking = reasoning_response.thinking_only()
        print(f"Thinking: {thinking}")
        
    except Exception as e:
        print(f"Error: {e}")
    
    # Example 3: Get only the final answer
    print("\n3. Only final answer:")
    try:
        reasoning_response = client.get_reasoning_answer(user_prompt=prompt)
        answer = reasoning_response.answer_only()
        print(f"Answer: {answer}")
        
    except Exception as e:
        print(f"Error: {e}")
    
    # Example 4: String representation
    print("\n4. String representation:")
    try:
        reasoning_response = client.get_reasoning_answer(user_prompt=prompt)
        print("Full response as string:")
        print(reasoning_response)
        
    except Exception as e:
        print(f"Error: {e}")


def example_reasoning_models():
    """Test different reasoning models."""
    print("\n=== Different Reasoning Models ===\n")
    
    reasoning_models = ["deepseek-reasoning", "o1-preview", "o1-mini"]
    
    for model_name in reasoning_models:
        print(f"\nTesting {model_name}:")
        try:
            client = FlowAPIClient(model_name)
            
            if client.supports_reasoning():
                print(f"✅ {model_name} supports reasoning")
                
                response = client.get_reasoning_answer(
                    user_prompt="Calculate the area of a circle with radius 5"
                )
                
                if response.has_thinking():
                    print(f"Thinking: {response.thinking[:100]}...")
                print(f"Answer: {response.content}")
            else:
                print(f"❌ {model_name} does not support reasoning")
                
        except Exception as e:
            print(f"Error with {model_name}: {e}")


def example_complex_reasoning():
    """Demonstrate reasoning with complex problems."""
    print("\n=== Complex Reasoning Problems ===\n")
    
    client = FlowAPIClient("deepseek-reasoning")
    
    complex_problems = [
        "A train leaves Station A at 2 PM traveling at 60 mph. Another train leaves Station B at 3 PM traveling at 80 mph toward Station A. If the stations are 280 miles apart, at what time will they meet?",
        "If you have a 3-gallon jug and a 5-gallon jug, how can you measure exactly 4 gallons of water?",
        "What is the next number in this sequence: 2, 6, 12, 20, 30, ?"
    ]
    
    for i, problem in enumerate(complex_problems, 1):
        print(f"\nProblem {i}: {problem}")
        try:
            response = client.get_reasoning_answer(user_prompt=problem)
            
            if response.has_thinking():
                print(f"Reasoning process: {response.thinking}")
            print(f"Solution: {response.content}")
            print("-" * 50)
            
        except Exception as e:
            print(f"Error: {e}")


def example_reasoning_with_streaming():
    """Demonstrate reasoning models with streaming (combined functionality)."""
    print("\n=== Reasoning + Streaming ===\n")
    
    # Use reasoning model with streaming
    streaming_client = StreamingClient("deepseek-reasoning")
    
    print("Streaming reasoning response:")
    try:
        print("Response: ", end="", flush=True)
        for chunk in streaming_client.stream(
            user_prompt="Think through this problem: If a train travels 60 mph for 2.5 hours, how far does it go?"
        ):
            print(chunk, end="", flush=True)
        print("\n")
        
        print("\nNote: In streaming mode, thinking and answer are mixed together.")
        print("For separated thinking/answer, use the regular reasoning client.")
        
    except Exception as e:
        print(f"Error: {e}")


def example_reasoning_comparison():
    """Compare reasoning vs regular models."""
    print("\n=== Reasoning vs Regular Models ===\n")
    
    problem = "Explain why 0.999... equals 1"
    
    print("Regular model response:")
    try:
        regular_client = FlowAPIClient("gpt-4")
        regular_response = regular_client.send(user_prompt=problem)
        content = regular_response["choices"][0]["message"]["content"]
        print(f"Answer: {content}")
        
    except Exception as e:
        print(f"Error: {e}")
    
    print("\nReasoning model response:")
    try:
        reasoning_client = FlowAPIClient("deepseek-reasoning")
        reasoning_response = reasoning_client.get_reasoning_answer(user_prompt=problem)
        
        if reasoning_response.has_thinking():
            print(f"Thinking: {reasoning_response.thinking}")
        print(f"Answer: {reasoning_response.content}")
        
    except Exception as e:
        print(f"Error: {e}")


def example_reasoning_with_system_prompt():
    """Demonstrate reasoning with system prompts."""
    print("\n=== Reasoning with System Prompt ===\n")
    
    client = FlowAPIClient("deepseek-reasoning")
    
    system_prompt = "You are a math tutor. Always show your work step by step."
    user_prompt = "Solve: 2x + 5 = 13"
    
    try:
        response = client.get_reasoning_answer(
            system_prompt=system_prompt,
            user_prompt=user_prompt
        )
        
        print("Math tutor reasoning:")
        if response.has_thinking():
            print(f"Work shown: {response.thinking}")
        print(f"Final answer: {response.content}")
        
    except Exception as e:
        print(f"Error: {e}")


def main():
    """Run all reasoning examples."""
    print("Flow API - Reasoning Models Examples")
    print("=" * 40)
    
    # Check if environment is configured
    if not os.getenv('FLOW_CLIENT_ID') or not os.getenv('FLOW_CLIENT_SECRET'):
        print("⚠️  Please set FLOW_CLIENT_ID and FLOW_CLIENT_SECRET environment variables")
        print("These examples require valid Flow API credentials.")
        return
    
    try:
        # Run examples
        example_basic_reasoning()
        example_reasoning_models()
        example_complex_reasoning()
        example_reasoning_with_streaming()
        example_reasoning_comparison()
        example_reasoning_with_system_prompt()
        
        print("\n✅ All reasoning examples completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Error running examples: {e}")
        print("Make sure you have valid credentials and network access.")


if __name__ == "__main__":
    main()
