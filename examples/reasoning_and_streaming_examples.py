"""
Examples demonstrating reasoning models and improved streaming functionality.

This script shows how to use the new features:
1. Reasoning models with separated thinking and final answer
2. Improved streaming with Amazon Bedrock support
3. Dedicated streaming client
"""

import os
from flow_api import FlowAPIClient
from flow_api.adapters.inbound.streaming_client import StreamingClient


def example_reasoning_models():
    """Demonstrate reasoning model capabilities."""
    print("=== Reasoning Models Examples ===\n")
    
    # Initialize client with a reasoning model
    client = FlowAPIClient("deepseek-reasoning")
    
    # Check if current model supports reasoning
    if client.supports_reasoning():
        print("✅ Current model supports reasoning capabilities")
    else:
        print("❌ Current model does not support reasoning")
    
    prompt = "Solve this step by step: What is 15% of 240?"
    
    # Example 1: Get complete reasoning response
    print("\n1. Complete reasoning response:")
    try:
        reasoning_response = client.get_reasoning_response(user_prompt=prompt)
        
        print(f"Thinking process: {reasoning_response.get('thinking', 'No thinking available')}")
        print(f"Final answer: {reasoning_response.get('content', 'No answer available')}")
        
    except Exception as e:
        print(f"Error: {e}")
    
    # Example 2: Get only the thinking process
    print("\n2. Only thinking process:")
    try:
        thinking = client.get_thinking_only(user_prompt=prompt)
        print(f"Thinking: {thinking}")
        
    except Exception as e:
        print(f"Error: {e}")
    
    # Example 3: Get only the final answer
    print("\n3. Only final answer:")
    try:
        answer = client.get_answer_only(user_prompt=prompt)
        print(f"Answer: {answer}")
        
    except Exception as e:
        print(f"Error: {e}")


def example_improved_streaming():
    """Demonstrate improved streaming with Amazon Bedrock support."""
    print("\n=== Improved Streaming Examples ===\n")
    
    # Example with OpenAI model
    print("1. Streaming with OpenAI model:")
    client = FlowAPIClient("gpt-4")
    
    try:
        print("Response: ", end="", flush=True)
        for chunk in client.get_stream_answer(user_prompt="Count from 1 to 5"):
            print(chunk, end="", flush=True)
        print("\n")
        
    except Exception as e:
        print(f"Error: {e}")
    
    # Example with Amazon Bedrock model (improved parsing)
    print("2. Streaming with Amazon Bedrock model:")
    client = FlowAPIClient("claude-3-sonnet")
    
    try:
        print("Response: ", end="", flush=True)
        for chunk in client.get_stream_answer(user_prompt="Explain AI in one sentence"):
            print(chunk, end="", flush=True)
        print("\n")
        
    except Exception as e:
        print(f"Error: {e}")


def example_dedicated_streaming_client():
    """Demonstrate the dedicated streaming client."""
    print("\n=== Dedicated Streaming Client Examples ===\n")
    
    # Create a dedicated streaming client
    streaming_client = StreamingClient("gpt-4")
    
    # Check streaming support
    if streaming_client.supports_streaming():
        print("✅ Model supports streaming")
    else:
        print("❌ Model does not support streaming")
        return
    
    print(f"Current model: {streaming_client.get_current_model()}")
    
    # Stream a response
    print("\nStreaming response: ", end="", flush=True)
    try:
        for chunk in streaming_client.stream(user_prompt="What is machine learning?"):
            print(chunk, end="", flush=True)
        print("\n")
        
    except Exception as e:
        print(f"Error: {e}")


def example_streaming_property():
    """Demonstrate using streaming through the main client property."""
    print("\n=== Streaming Property Examples ===\n")
    
    # Use streaming through the main client
    client = FlowAPIClient("gpt-4")
    
    print("Using client.streaming property:")
    try:
        print("Response: ", end="", flush=True)
        for chunk in client.streaming.stream(user_prompt="List 3 benefits of AI"):
            print(chunk, end="", flush=True)
        print("\n")
        
    except Exception as e:
        print(f"Error: {e}")


def example_reasoning_with_streaming():
    """Demonstrate reasoning models with streaming."""
    print("\n=== Reasoning + Streaming Examples ===\n")
    
    # Use reasoning model with streaming
    client = FlowAPIClient("deepseek-reasoning")
    
    if not client.supports_reasoning():
        print("❌ Model does not support reasoning, using regular model")
        client = FlowAPIClient("gpt-4")
    
    print("Streaming reasoning response:")
    try:
        print("Response: ", end="", flush=True)
        for chunk in client.streaming.stream(
            user_prompt="Think through this problem: If a train travels 60 mph for 2.5 hours, how far does it go?"
        ):
            print(chunk, end="", flush=True)
        print("\n")
        
    except Exception as e:
        print(f"Error: {e}")


def main():
    """Run all examples."""
    print("Flow API - Reasoning Models and Streaming Examples")
    print("=" * 55)
    
    # Check if environment is configured
    if not os.getenv('FLOW_CLIENT_ID') or not os.getenv('FLOW_CLIENT_SECRET'):
        print("⚠️  Please set FLOW_CLIENT_ID and FLOW_CLIENT_SECRET environment variables")
        print("These examples require valid Flow API credentials.")
        return
    
    try:
        # Run examples
        example_reasoning_models()
        example_improved_streaming()
        example_dedicated_streaming_client()
        example_streaming_property()
        example_reasoning_with_streaming()
        
        print("\n✅ All examples completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Error running examples: {e}")
        print("Make sure you have valid credentials and network access.")


if __name__ == "__main__":
    main()
