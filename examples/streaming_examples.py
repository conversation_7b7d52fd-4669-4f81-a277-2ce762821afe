"""
Examples demonstrating streaming functionality with the dedicated StreamingClient.

This script shows how to use the StreamingClient for real-time responses from AI models.
"""

import os
from flow_api.adapters.inbound.streaming_client import StreamingClient


def example_basic_streaming():
    """Demonstrate basic streaming functionality."""
    print("=== Basic Streaming Examples ===\n")
    
    # Create a dedicated streaming client
    streaming_client = StreamingClient("gpt-4")
    
    # Check streaming support
    if streaming_client.supports_streaming():
        print("✅ Model supports streaming")
    else:
        print("❌ Model does not support streaming")
        return
    
    print(f"Current model: {streaming_client.get_current_model()}")
    
    # Stream a simple response
    print("\n1. Simple streaming:")
    print("Response: ", end="", flush=True)
    try:
        for chunk in streaming_client.stream(user_prompt="Count from 1 to 5"):
            print(chunk, end="", flush=True)
        print("\n")
        
    except Exception as e:
        print(f"Error: {e}")


def example_openai_streaming():
    """Demonstrate streaming with OpenAI models."""
    print("\n=== OpenAI Streaming ===\n")
    
    streaming_client = StreamingClient("gpt-4")
    
    print("Streaming with OpenAI model:")
    try:
        print("Response: ", end="", flush=True)
        for chunk in streaming_client.stream(user_prompt="Tell me a short joke"):
            print(chunk, end="", flush=True)
        print("\n")
        
    except Exception as e:
        print(f"Error: {e}")


def example_bedrock_streaming():
    """Demonstrate streaming with Amazon Bedrock models (improved parsing)."""
    print("\n=== Amazon Bedrock Streaming ===\n")
    
    streaming_client = StreamingClient("claude-3-sonnet")
    
    print("Streaming with Amazon Bedrock model:")
    try:
        print("Response: ", end="", flush=True)
        for chunk in streaming_client.stream(user_prompt="Explain AI in one sentence"):
            print(chunk, end="", flush=True)
        print("\n")
        
    except Exception as e:
        print(f"Error: {e}")


def example_model_switching():
    """Demonstrate switching between different models."""
    print("\n=== Model Switching ===\n")
    
    streaming_client = StreamingClient()
    
    # Test with different models
    models = ["gpt-4", "claude-3-sonnet", "gemini-pro"]
    
    for model in models:
        try:
            streaming_client.with_model(model)
            print(f"\nTesting with {model}:")
            
            if streaming_client.supports_streaming():
                print("Response: ", end="", flush=True)
                for chunk in streaming_client.stream(user_prompt="Say hello"):
                    print(chunk, end="", flush=True)
                print()
            else:
                print(f"❌ {model} does not support streaming")
                
        except Exception as e:
            print(f"Error with {model}: {e}")


def example_streaming_with_system_prompt():
    """Demonstrate streaming with system prompts."""
    print("\n=== Streaming with System Prompt ===\n")
    
    streaming_client = StreamingClient("gpt-4")
    
    system_prompt = "You are a helpful assistant that speaks like a pirate."
    user_prompt = "Tell me about the weather"
    
    print("Streaming with system prompt:")
    print("Response: ", end="", flush=True)
    try:
        for chunk in streaming_client.stream(
            system_prompt=system_prompt,
            user_prompt=user_prompt
        ):
            print(chunk, end="", flush=True)
        print("\n")
        
    except Exception as e:
        print(f"Error: {e}")


def example_streaming_parameters():
    """Demonstrate streaming with different parameters."""
    print("\n=== Streaming with Parameters ===\n")
    
    streaming_client = StreamingClient("gpt-4")
    
    print("Streaming with temperature=0.8:")
    print("Response: ", end="", flush=True)
    try:
        for chunk in streaming_client.stream(
            user_prompt="Write a creative story opening",
            temperature=0.8,
            max_tokens=100
        ):
            print(chunk, end="", flush=True)
        print("\n")
        
    except Exception as e:
        print(f"Error: {e}")


def main():
    """Run all streaming examples."""
    print("Flow API - Streaming Examples")
    print("=" * 35)
    
    # Check if environment is configured
    if not os.getenv('FLOW_CLIENT_ID') or not os.getenv('FLOW_CLIENT_SECRET'):
        print("⚠️  Please set FLOW_CLIENT_ID and FLOW_CLIENT_SECRET environment variables")
        print("These examples require valid Flow API credentials.")
        return
    
    try:
        # Run examples
        example_basic_streaming()
        example_openai_streaming()
        example_bedrock_streaming()
        example_model_switching()
        example_streaming_with_system_prompt()
        example_streaming_parameters()
        
        print("\n✅ All streaming examples completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Error running examples: {e}")
        print("Make sure you have valid credentials and network access.")


if __name__ == "__main__":
    main()
